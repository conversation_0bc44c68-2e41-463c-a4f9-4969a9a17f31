import { Provider } from "react-redux"
import { PersistGate } from "@plasmohq/redux-persist/integration/react"
import { ThemeProvider } from "~components/ui/theme-provider"
import { persistor, store } from "~store/store"
import "./style.css"

// 子组件，在Redux Provider内部使用
function SidePanelContent() {
  return (
    <div>
    </div>
  )
}

function SidePanel() {
  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          <SidePanelContent />
        </ThemeProvider>
      </PersistGate>
    </Provider>
  )
}

export default SidePanel 