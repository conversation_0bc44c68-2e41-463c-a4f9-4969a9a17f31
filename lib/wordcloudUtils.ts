interface KeywordData {
  keyword: string
  aliases: string[]
  ids: number[]
}

interface TitleData {
  id: number
  title: string
}

interface PostInfo {
  id: number
  title: string
  link?: string
  author?: string
  time?: string
  xpath?: string
}

interface WordCloudItem {
  text: string
  size: number
  relatedPosts?: PostInfo[]
}

/**
 * 从OpenAI返回的关键词数据生成词云数据
 * @param keywordsData OpenAI返回的关键词数据
 * @returns 词云数据数组
 */
export async function processKeywordsForWordCloud(keywordsData: any[]): Promise<WordCloudItem[]> {
  if (!keywordsData || !Array.isArray(keywordsData)) {
    console.error('[词云生成] 输入的关键词数据无效:', keywordsData);
    return [];
  }
  
  try {
    // 计算每个关键词的权重（基于关联的文章数量）
    const wordCloudData: WordCloudItem[] = keywordsData.map(item => {
      const weight = item.ids?.length || 0;
      
      // 创建关联帖子信息
      let relatedPosts;
      
      // 优先使用增强数据中的原始帖子信息
      if (item.originalPosts && Array.isArray(item.originalPosts)) {
        relatedPosts = item.originalPosts.map((post: any) => ({
          id: post.id,
          title: post.title,
          link: post.link,
          author: post.author,
          time: post.time,
          xpath: post.xpath
        }));
      } else if (item.ids && Array.isArray(item.ids)) {
        // 这里直接使用关键词数据中的ids，不再尝试匹配示例数据
        // 在实际场景中，我们需要确保这些ids对应的帖子信息可以从其他地方获取
        relatedPosts = item.ids.map((id: number, index: number) => {
          // 尝试从item.titles数组中获取标题（如果存在）
          const title = item.titles && item.titles[index] 
            ? item.titles[index] 
            : `相关文章 #${id}`;
            
          return { id, title };
        });
      }
      
      // 使用更合理的字体大小计算，确保有足够的差异
      return {
        text: item.keyword,
        size: Math.max(weight * 5 + 10, 15), // 基础大小15，每个关联文章增加5
        relatedPosts
      };
    });

    // 按权重排序，权重高的在前
    wordCloudData.sort((a, b) => b.size - a.size);
    
    // 限制为前15个
    return wordCloudData.slice(0, 15);
  } catch (error) {
    console.error('[词云生成] 处理关键词数据失败:', error);
    return [];
  }
}

/**
 * 处理关键词数据，生成词云数据
 * @param keywords 关键词数据
 * @param titles 标题数据（可选，用于获取关联帖子信息）
 * @returns 词云数据数组
 */
export function processKeywordsWithTitles(
  keywords: KeywordData[],
  titles?: TitleData[]
): WordCloudItem[] {
  // 计算每个关键词的权重（基于关联的文章数量）
  const wordCloudData: WordCloudItem[] = keywords.map(item => {
    const weight = item.ids.length
    
    // 获取关联的帖子信息
    const relatedPosts = titles ? 
      item.ids.map(id => {
        const title = titles.find(t => t.id === id)
        return title ? { id: title.id, title: title.title } : null
      }).filter(Boolean) as PostInfo[] : undefined
    
    // 使用更合理的字体大小计算，确保有足够的差异
    return {
      text: item.keyword,
      size: Math.max(weight * 5 + 10, 15), // 基础大小15，每个关联文章增加5
      relatedPosts
    }
  })

  // 按权重排序，权重高的在前
  wordCloudData.sort((a, b) => b.size - a.size)

  return wordCloudData
}