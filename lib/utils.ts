import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { jsonrepair } from 'jsonrepair';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * 安全解析JSON字符串，使用jsonrepair修复可能存在的格式问题
 * @param jsonString 要解析的JSON字符串
 * @param defaultValue 解析失败时返回的默认值
 * @returns 解析后的对象，或解析失败时的默认值
 */
export function safeParseJSON<T>(jsonString: string, defaultValue: T = null as unknown as T): T {
  try {
    if (!jsonString || typeof jsonString !== 'string') {
      return defaultValue;
    }

    const trimmed = jsonString.trim();

    // 如果字符串太短或只是开始字符，返回默认值
    if (trimmed.length < 2) {
      return defaultValue;
    }

    // 首先尝试直接解析
    try {
      return JSON.parse(trimmed);
    } catch (directError) {
      // 如果直接解析失败，尝试使用jsonrepair修复
      const repairedJson = jsonrepair(trimmed);
      return JSON.parse(repairedJson);
    }
  } catch (error) {
    // 静默处理错误，避免控制台噪音
    return defaultValue;
  }
}

/**
 * 从文本中提取并解析JSON
 * 支持从代码块或整个文本中提取
 * @param text 包含JSON的文本
 * @param defaultValue 解析失败时返回的默认值
 * @returns 解析后的对象，或解析失败时的默认值
 */
export function extractAndParseJSON<T>(text: string, defaultValue: T = null as unknown as T): T {
  try {
    if (!text || typeof text !== 'string') {
      return defaultValue;
    }

    // 清理文本，移除可能的markdown标记
    let cleanedText = text.trim();

    // 如果只是开始标记，返回默认值
    if (cleanedText === '```json' || cleanedText === '```') {
      return defaultValue;
    }

    // 移除markdown代码块标记
    cleanedText = cleanedText.replace(/^```json\s*/, '').replace(/\s*```$/, '');

    // 如果清理后为空或太短，返回默认值
    if (!cleanedText || cleanedText.length < 2) {
      return defaultValue;
    }

    // 尝试从文本中提取JSON部分
    let jsonStr = '';
    
    // 首先尝试提取完整的JSON数组
    const arrayMatch = cleanedText.match(/\[\s*\{[\s\S]*?\}\s*\]/);
    if (arrayMatch && arrayMatch[0]) {
      jsonStr = arrayMatch[0];
    } 
    // 如果没有完整数组匹配，尝试提取JSON对象
    else {
      const jsonMatch = cleanedText.match(/\[([\s\S]*?)\]/) ||
                        cleanedText.match(/\{([\s\S]*?)\}/);
                        
      if (jsonMatch && jsonMatch[0]) {
        // 使用完整匹配结果
        jsonStr = jsonMatch[0];
      } else if (cleanedText.startsWith('[') || cleanedText.startsWith('{')) {
        // 如果以JSON字符开始，使用整个清理后的文本
        jsonStr = cleanedText;
        
        // 检查是否是不完整的数组
        if (jsonStr.startsWith('[') && !jsonStr.includes(']')) {
          // 尝试找到最后一个完整对象
          if (jsonStr.includes('{') && jsonStr.includes('}')) {
            const lastCompleteObjectEnd = jsonStr.lastIndexOf('}');
            if (lastCompleteObjectEnd > -1) {
              jsonStr = jsonStr.substring(0, lastCompleteObjectEnd + 1) + ']';
            }
          }
        }
      } else {
        // 尝试提取所有可能的JSON对象
        const objectMatches = cleanedText.match(/\{[^{}]*\}/g);
        if (objectMatches && objectMatches.length > 0) {
          // 将找到的所有对象包装成数组
          jsonStr = '[' + objectMatches.join(',') + ']';
        } else {
          // 否则返回默认值
          return defaultValue;
        }
      }
    }

    // 使用safeParseJSON解析
    return safeParseJSON(jsonStr, defaultValue);
  } catch (error) {
    // 静默处理错误，避免控制台噪音
    return defaultValue;
  }
}