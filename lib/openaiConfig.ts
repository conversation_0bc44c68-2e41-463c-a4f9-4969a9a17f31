// OpenAI 配置接口
export interface OpenAIConfig {
  apiKey: string;
  baseURL?: string;
  model: string;
  maxTokens: number;
  temperature: number;
}

// 默认配置
export const DEFAULT_OPENAI_CONFIG: Partial<OpenAIConfig> = {
  model: 'gpt-3.5-turbo',
  maxTokens: 1000,
  temperature: 0.7,
  baseURL: 'https://api.openai.com/v1'
};

// 常用模型列表
export const OPENAI_MODELS = {
  GPT_3_5_TURBO: 'gpt-3.5-turbo',
  GPT_4: 'gpt-4',
  GPT_4_TURBO: 'gpt-4-turbo-preview',
  GPT_4O: 'gpt-4o',
  GPT_4O_MINI: 'gpt-4o-mini'
} as const;

// 存储键名
export const STORAGE_KEYS = {
  OPENAI_API_KEY: 'openai_api_key',
  OPENAI_BASE_URL: 'openai_base_url',
  OPENAI_MODEL: 'openai_model',
  OPENAI_MAX_TOKENS: 'openai_max_tokens',
  OPENAI_TEMPERATURE: 'openai_temperature'
} as const;

/**
 * 从存储中加载 OpenAI 配置
 */
export async function loadOpenAIConfig(): Promise<Partial<OpenAIConfig>> {
  try {
    // 使用 Chrome 存储 API
    const result = await chrome.storage.sync.get([
      STORAGE_KEYS.OPENAI_API_KEY,
      STORAGE_KEYS.OPENAI_BASE_URL,
      STORAGE_KEYS.OPENAI_MODEL,
      STORAGE_KEYS.OPENAI_MAX_TOKENS,
      STORAGE_KEYS.OPENAI_TEMPERATURE
    ]);

    return {
      apiKey: result[STORAGE_KEYS.OPENAI_API_KEY] || '',
      baseURL: result[STORAGE_KEYS.OPENAI_BASE_URL] || DEFAULT_OPENAI_CONFIG.baseURL,
      model: result[STORAGE_KEYS.OPENAI_MODEL] || DEFAULT_OPENAI_CONFIG.model,
      maxTokens: result[STORAGE_KEYS.OPENAI_MAX_TOKENS] || DEFAULT_OPENAI_CONFIG.maxTokens,
      temperature: result[STORAGE_KEYS.OPENAI_TEMPERATURE] || DEFAULT_OPENAI_CONFIG.temperature
    };
  } catch (error) {
    console.error('加载 OpenAI 配置失败:', error);
    return DEFAULT_OPENAI_CONFIG;
  }
}

/**
 * 保存 OpenAI 配置到存储
 */
export async function saveOpenAIConfig(config: Partial<OpenAIConfig>): Promise<void> {
  try {
    const dataToSave: Record<string, any> = {};
    
    if (config.apiKey !== undefined) {
      dataToSave[STORAGE_KEYS.OPENAI_API_KEY] = config.apiKey;
    }
    if (config.baseURL !== undefined) {
      dataToSave[STORAGE_KEYS.OPENAI_BASE_URL] = config.baseURL;
    }
    if (config.model !== undefined) {
      dataToSave[STORAGE_KEYS.OPENAI_MODEL] = config.model;
    }
    if (config.maxTokens !== undefined) {
      dataToSave[STORAGE_KEYS.OPENAI_MAX_TOKENS] = config.maxTokens;
    }
    if (config.temperature !== undefined) {
      dataToSave[STORAGE_KEYS.OPENAI_TEMPERATURE] = config.temperature;
    }

    await chrome.storage.sync.set(dataToSave);
  } catch (error) {
    console.error('保存 OpenAI 配置失败:', error);
    throw error;
  }
}

/**
 * 验证 OpenAI 配置是否有效
 */
export function validateOpenAIConfig(config: Partial<OpenAIConfig>): string[] {
  const errors: string[] = [];

  if (!config.apiKey || config.apiKey.trim() === '') {
    errors.push('API Key 不能为空');
  }

  if (config.maxTokens && (config.maxTokens < 1 || config.maxTokens > 4096)) {
    errors.push('最大 Token 数必须在 1-4096 之间');
  }

  if (config.temperature && (config.temperature < 0 || config.temperature > 2)) {
    errors.push('温度值必须在 0-2 之间');
  }

  return errors;
}
