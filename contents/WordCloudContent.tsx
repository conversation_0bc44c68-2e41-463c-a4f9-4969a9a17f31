import cssText from "data-text:~style.css"
import type { PlasmoCSConfig, PlasmoGetStyle } from "plasmo"
import WordCloudPanel from "../components/wordcloud/WordCloudPanel"

// 导出CSS
export const getStyle: PlasmoGetStyle = () => {
  const style = document.createElement("style")
  style.textContent = cssText
  return style
}

// 配置内容脚本 - 在所有网站上运行
export const config: PlasmoCSConfig = {
  matches: ["<all_urls>"],
  all_frames: false,
  run_at: "document_end"
}

// 使用默认导出组件，Plasmo会自动使用Shadow DOM进行样式隔离
function WordCloudContent() {
  return <WordCloudPanel />
}

export default WordCloudContent