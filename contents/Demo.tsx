import cssText from "data-text:~style.css"
import type { PlasmoCSConfig, PlasmoGetStyle } from "plasmo"

// 导出CSS
export const getStyle: PlasmoGetStyle = () => {
  const style = document.createElement("style")
  style.textContent = cssText
  return style
}

// 配置内容脚本
export const config: PlasmoCSConfig = {
  matches: ["https://www.xxx.com/*"],
  all_frames: false
}

function Demo() {
  return <div>Demo</div>
}

export default Demo
