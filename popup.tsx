import { useState } from "react"
import { Provider } from "react-redux"
import { PersistGate } from "@plasmohq/redux-persist/integration/react"
import { ChevronDown, ChevronUp } from "lucide-react"
import { ThemeProvider } from "./components/ui/theme-provider"

import { persistor, store } from "~store/store"
import "./style.css"
import Common from "~components/popup/Common"

function IndexPopup() {
  const [activeTab, setActiveTab] = useState<"common" | "scraper">("common")
  const [isTabsVisible, setIsTabsVisible] = useState(true)

  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          <div className="w-[400px] p-4 bg-white dark:bg-gray-900 dark:text-white transition-colors relative">
            {/* 标签切换控制图标 */}
            <div className="absolute top-0 left-0 flex justify-start">
              <button
                onClick={() => setIsTabsVisible(!isTabsVisible)}
                className="text-xs p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                title={isTabsVisible ? "折叠标签栏" : "展开标签栏"}
              >
                {isTabsVisible ? (
                  <ChevronUp size={20} className="text-gray-300 dark:text-gray-300" />
                ) : (
                  <ChevronDown size={20} className="text-gray-300 dark:text-gray-300" />
                )}
              </button>
            </div>
            
            {/* 标签切换 */}
            {isTabsVisible && (
              <div className="flex border-b mb-4">
                <button
                  className={`py-2 px-4 ${
                    activeTab === "common"
                      ? "border-b-2 border-blue-500 font-medium"
                      : "text-gray-500"
                  }`}
                  onClick={() => setActiveTab("common")}
                >通用操作</button>


              </div>
            )}
            
            {activeTab === "common" && (
              <Common />
            )}


          </div>
        </ThemeProvider>
      </PersistGate>
    </Provider>
  )
}

export default IndexPopup
