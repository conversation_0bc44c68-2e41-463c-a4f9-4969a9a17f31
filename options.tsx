import { useState } from "react"
import { Provider } from "react-redux"
import { PersistGate } from "@plasmohq/redux-persist/integration/react"
import { persistor, store } from "~store/store"
import { OpenAISettings } from "./components/OpenAISettings"
import { ThemeProvider } from "./components/ui/theme-provider"
import "./style.css"

function OptionsIndex() {
  const [activeTab, setActiveTab] = useState("openai")

  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <ThemeProvider defaultTheme="light" storageKey="browseforme-theme">
          <div className="min-h-screen bg-background">
            <div className="container mx-auto py-8">
              <div className="mb-8">
                <h1 className="text-3xl font-bold text-foreground">BrowseForMe 设置</h1>
                <p className="text-muted-foreground mt-2">配置你的浏览器扩展</p>
              </div>

              <div className="flex gap-6">
                {/* 侧边栏导航 */}
                <div className="w-64">
                  <nav className="space-y-2">
                    <button
                      onClick={() => setActiveTab("openai")}
                      className={`w-full text-left px-4 py-2 rounded-lg transition-colors ${
                        activeTab === "openai"
                          ? "bg-primary text-primary-foreground"
                          : "hover:bg-muted"
                      }`}
                    >
                      OpenAI 设置
                    </button>
                    <button
                      onClick={() => setActiveTab("general")}
                      className={`w-full text-left px-4 py-2 rounded-lg transition-colors ${
                        activeTab === "general"
                          ? "bg-primary text-primary-foreground"
                          : "hover:bg-muted"
                      }`}
                    >
                      常规设置
                    </button>
                  </nav>
                </div>

                {/* 主内容区域 */}
                <div className="flex-1">
                  {activeTab === "openai" && <OpenAISettings />}
                  {activeTab === "general" && (
                    <div className="bg-card p-6 rounded-lg border">
                      <h2 className="text-xl font-semibold mb-4">常规设置</h2>
                      <p className="text-muted-foreground">其他设置选项将在这里显示...</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </ThemeProvider>
      </PersistGate>
    </Provider>
  )
}

export default OptionsIndex