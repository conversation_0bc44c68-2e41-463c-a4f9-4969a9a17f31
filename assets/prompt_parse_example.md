## 帖子列表
```json
[
    {
        "id": 1,
        "title": "BitTrans - 使用动态二维码传数据，离线、安全、开源"
    },
    {
        "id": 2,
        "title": "颠覆传统：忘掉“学”英语，开始“接触”英语！一键给你的浏览器装一个“英语环境”生成器"
    },
    {
        "id": 3,
        "title": "#1 Flux AI：革命性的 AI 图像生成与编辑平台"
    }
]
```

## 任务
提取以下帖子列表里包含的关键词信息。

## 返回格式
你必须严格按照以下JSON格式返回结果，不要添加任何额外的文本、解释或标记：

```json
[
    {
        "keyword": "关键词",
        "aliases": [
            "关键词别名1",
            "关键词别名2"
        ],
        "ids": [1, 2, 3]
    }
]
```

## 重要说明
1. 只返回JSON数组，不要有任何其他文本
2. 确保JSON格式正确，可以被解析
3. 每个关键词对象必须包含keyword和ids字段
4. ids字段必须是数字数组，表示包含该关键词的帖子ID
5. aliases字段可选，如果没有别名可以省略
6. 按关键词包含的帖子数量递减排序
