import React from 'react'
import { FileText, X, MapPin } from 'lucide-react'
import { navigateToElementByXPath } from '../../lib/postDetection'

interface PostInfo {
  id: number
  title: string
  link?: string
  author?: string
  time?: string
  xpath?: string
}

interface DetailPanelProps {
  hoveredWord: string | null
  hoveredPosts: PostInfo[] | null
  onPostClick?: (post: PostInfo) => void
  onClose?: () => void
  isFixed?: boolean
}

const DetailPanel: React.FC<DetailPanelProps> = ({
  hoveredWord,
  hoveredPosts,
  onPostClick,
  onClose,
  isFixed = false
}) => {
  if (!hoveredPosts || hoveredPosts.length === 0 || !hoveredWord) {
    return null
  }

  const handlePostClick = (post: PostInfo) => {
    console.log('点击帖子:', post)
    
    // 调用外部传入的点击处理函数
    onPostClick?.(post)
    
    // 不再在这里处理链接打开，而是由父组件统一处理
  }

  const handleNavigateToPost = (e: React.MouseEvent, post: PostInfo) => {
    e.stopPropagation()
    
    if (post.xpath) {
      console.log('导航到帖子位置:', post.xpath)
      navigateToElementByXPath(post.xpath)
    } else {
      console.warn('该帖子没有有效的XPath路径')
    }
  }

  return (
    <div className={`bg-white rounded-lg shadow-lg w-96 max-h-96 overflow-hidden transition-all duration-200 ${
      isFixed ? 'border-2 border-solid border-gray-200' : 'border-2 border-dashed border-gray-300'
    }`}>
      <div className="flex items-center justify-between px-3 py-2 border-b border-gray-100 min-h-[44px]">
        <div className="flex items-center gap-2">
          <FileText className="w-4 h-4 text-green-500" />
          <span className="text-sm font-medium text-gray-700">
            详情面板 - "{hoveredWord}" 相关帖子 ({hoveredPosts.length})
          </span>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded transition-colors"
            title="关闭详情面板"
          >
            <X className="w-4 h-4 text-gray-500" />
          </button>
        )}
      </div>
      <div className="px-3 py-2 max-h-80 overflow-y-auto space-y-2">
        {hoveredPosts.map((post) => (
          <div
            key={post.id}
            className="text-sm text-gray-600 hover:text-blue-600 cursor-pointer px-3 py-2 hover:bg-gray-50 rounded transition-colors border border-gray-100 flex justify-between items-center group"
            onClick={() => handlePostClick(post)}
          >
            <div className="flex-1 min-w-0">
              <div className="leading-5">
                {post.link ? (
                  <a
                    href={post.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline"
                    onClick={e => e.stopPropagation()}
                  >
                    {post.title}
                  </a>
                ) : (
                  post.title
                )}
              </div>
            </div>

            {post.xpath && (
              <button
                className="p-1 rounded-full hover:bg-gray-200 transition-colors opacity-0 group-hover:opacity-100 ml-2 flex-shrink-0 h-5 w-5 flex items-center justify-center"
                onClick={(e) => handleNavigateToPost(e, post)}
                title="定位到帖子位置"
              >
                <MapPin className="w-3.5 h-3.5 text-gray-500 hover:text-blue-500" />
              </button>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}

export default DetailPanel 