import React, { useState, useEffect } from 'react';
import { But<PERSON> } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Slider } from './ui/slider';
import { Alert, AlertDescription } from './ui/alert';
import { Loader2, Check, AlertCircle } from 'lucide-react';
import { 
  loadOpenAIConfig, 
  saveOpenAIConfig, 
  validateOpenAIConfig, 
  OPENAI_MODELS,
  type OpenAIConfig 
} from '../lib/openaiConfig';
import { OpenAIService, initializeOpenAI } from '../lib/openaiService';

export function OpenAISettings() {
  const [config, setConfig] = useState<Partial<OpenAIConfig>>({
    apiKey: '',
    baseURL: 'https://api.openai.com/v1',
    model: OPENAI_MODELS.GPT_4O_MINI,
    maxTokens: 1000,
    temperature: 0.7
  });
  
  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState(false);
  const [saved, setSaved] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);

  // 加载配置
  useEffect(() => {
    loadConfig();
  }, []);

  const loadConfig = async () => {
    try {
      setLoading(true);
      const loadedConfig = await loadOpenAIConfig();
      setConfig(loadedConfig);
    } catch (error) {
      console.error('加载配置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 保存配置
  const handleSave = async () => {
    try {
      setLoading(true);
      setErrors([]);
      
      // 验证配置
      const validationErrors = validateOpenAIConfig(config);
      if (validationErrors.length > 0) {
        setErrors(validationErrors);
        return;
      }

      // 保存配置
      await saveOpenAIConfig(config);
      
      // 初始化OpenAI服务
      if (config.apiKey) {
        try {
          initializeOpenAI(config.apiKey, config.baseURL);
          console.log('已成功初始化OpenAI服务');
        } catch (error) {
          console.error('初始化OpenAI服务失败:', error);
        }
      }
      
      setSaved(true);
      
      // 3秒后隐藏保存成功提示
      setTimeout(() => setSaved(false), 3000);
      
    } catch (error) {
      console.error('保存配置失败:', error);
      setErrors(['保存配置失败，请重试']);
    } finally {
      setLoading(false);
    }
  };

  // 测试连接
  const handleTest = async () => {
    try {
      setTesting(true);
      setTestResult(null);
      
      if (!config.apiKey) {
        setTestResult({ success: false, message: '请先输入 API Key' });
        return;
      }

      // 初始化OpenAI服务
      initializeOpenAI(config.apiKey, config.baseURL);
      
      const service = new OpenAIService(config.apiKey, config.baseURL);
      const response = await service.generateText('测试连接成功', config.model, 10);
      
      if (response) {
        setTestResult({ success: true, message: 'API 连接测试成功！配置有效。' });
        
        // 自动保存成功的配置
        if (!saved) {
          await saveOpenAIConfig(config);
          setSaved(true);
          setTimeout(() => setSaved(false), 3000);
        }
      } else {
        setTestResult({ success: false, message: 'API 返回空响应' });
      }
      
    } catch (error: any) {
      console.error('测试连接失败:', error);
      setTestResult({ 
        success: false, 
        message: `连接失败: ${error.message || '未知错误'}` 
      });
    } finally {
      setTesting(false);
    }
  };

  const updateConfig = (key: keyof OpenAIConfig, value: any) => {
    setConfig(prev => ({ ...prev, [key]: value }));
    setSaved(false);
    setTestResult(null);
  };

  if (loading && !config.apiKey) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-6 w-6 animate-spin" />
        <span className="ml-2">加载配置中...</span>
      </div>
    );
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>OpenAI 设置</CardTitle>
        <CardDescription>
          配置 OpenAI API 以启用 AI 功能，如关键词提取、文章分析等
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* 错误提示 */}
        {errors.length > 0 && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <ul className="list-disc list-inside">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        {/* 保存成功提示 */}
        {saved && (
          <Alert>
            <Check className="h-4 w-4" />
            <AlertDescription>配置已保存成功！</AlertDescription>
          </Alert>
        )}

        {/* 测试结果 */}
        {testResult && (
          <Alert variant={testResult.success ? "default" : "destructive"}>
            {testResult.success ? (
              <Check className="h-4 w-4" />
            ) : (
              <AlertCircle className="h-4 w-4" />
            )}
            <AlertDescription>{testResult.message}</AlertDescription>
          </Alert>
        )}

        {/* API Key */}
        <div className="space-y-2">
          <Label htmlFor="apiKey">API Key *</Label>
          <Input
            id="apiKey"
            type="password"
            placeholder="sk-..."
            value={config.apiKey || ''}
            onChange={(e) => updateConfig('apiKey', e.target.value)}
          />
          <p className="text-sm text-muted-foreground">
            从 OpenAI 官网获取你的 API Key
          </p>
        </div>

        {/* Base URL */}
        <div className="space-y-2">
          <Label htmlFor="baseURL">Base URL</Label>
          <Input
            id="baseURL"
            placeholder="https://api.openai.com/v1"
            value={config.baseURL || ''}
            onChange={(e) => updateConfig('baseURL', e.target.value)}
          />
          <p className="text-sm text-muted-foreground">
            可选：使用自定义的 API 端点
          </p>
        </div>

        {/* 模型选择 */}
        <div className="space-y-2">
          <Label>模型</Label>
          <Select
            value={config.model}
            onValueChange={(value) => updateConfig('model', value)}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={OPENAI_MODELS.GPT_4O_MINI}>GPT-4o Mini</SelectItem>
              <SelectItem value={OPENAI_MODELS.GPT_4O}>GPT-4o</SelectItem>
              <SelectItem value={OPENAI_MODELS.GPT_4_TURBO}>GPT-4 Turbo</SelectItem>
              <SelectItem value={OPENAI_MODELS.GPT_4}>GPT-4</SelectItem>
              <SelectItem value={OPENAI_MODELS.GPT_3_5_TURBO}>GPT-3.5 Turbo</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* 最大 Token 数 */}
        <div className="space-y-2">
          <Label>最大 Token 数: {config.maxTokens}</Label>
          <Slider
            value={[config.maxTokens || 1000]}
            onValueChange={([value]) => updateConfig('maxTokens', value)}
            max={4096}
            min={100}
            step={100}
            className="w-full"
          />
          <p className="text-sm text-muted-foreground">
            控制生成内容的最大长度
          </p>
        </div>

        {/* 温度 */}
        <div className="space-y-2">
          <Label>创造性 (Temperature): {config.temperature}</Label>
          <Slider
            value={[config.temperature || 0.7]}
            onValueChange={([value]) => updateConfig('temperature', value)}
            max={2}
            min={0}
            step={0.1}
            className="w-full"
          />
          <p className="text-sm text-muted-foreground">
            0 = 更保守，2 = 更有创造性
          </p>
        </div>

        {/* 操作按钮 */}
        <div className="flex gap-3 pt-4">
          <Button 
            onClick={handleTest} 
            variant="outline" 
            disabled={testing || !config.apiKey}
          >
            {testing ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                测试中...
              </>
            ) : (
              '测试连接'
            )}
          </Button>
          
          <Button 
            onClick={handleSave} 
            disabled={loading}
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                保存中...
              </>
            ) : (
              '保存配置'
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
