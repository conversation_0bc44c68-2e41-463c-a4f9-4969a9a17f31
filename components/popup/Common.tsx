import { Button } from "~components/ui/button";

const Common = () => {
  return (
    <div className="flex gap-2">
      <Button onClick={() => {
        chrome.sidePanel.setOptions({ enabled: true });
        chrome.tabs.query({ active: true, currentWindow: true }, ([tab]) => {
          chrome.sidePanel.open({ tabId: tab.id });
        });
      }}>打开侧边栏</Button>
      <Button onClick={() => {
        chrome.sidePanel.setOptions({ enabled: false });
      }}>关闭侧边栏</Button>

    </div>
  )
}

export default Common